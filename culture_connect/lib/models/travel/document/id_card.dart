import 'package:culture_connect/models/travel/document/travel_document_base.dart';
import 'package:intl/intl.dart';

/// A model representing an ID card
class IdCard extends TravelDocument {
  /// Full name on the ID card
  final String fullName;

  /// Date of birth
  final DateTime dateOfBirth;

  /// Gender
  final String gender;

  /// Address on the ID card
  final String address;

  /// Nationality
  final String nationality;

  /// ID card type (National ID, Driver's License, etc.)
  final IdCardType idCardType;

  /// Creates a new ID card
  const IdCard({
    required super.id,
    required super.userId,
    required super.name,
    required super.documentNumber,
    required super.issuedBy,
    required super.issuedDate,
    required super.expiryDate,
    required super.status,
    super.notes,
    required super.documentImageUrls,
    required super.createdAt,
    required super.updatedAt,
    required this.fullName,
    required this.dateOfBirth,
    required this.gender,
    required this.address,
    required this.nationality,
    required this.idCardType,
  }) : super(type: TravelDocumentType.idCard);

  /// Get the formatted date of birth
  String get formattedDateOfBirth {
    return DateFormat('MMM dd, yyyy').format(dateOfBirth);
  }

  /// Get the age based on date of birth
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  /// Create an ID card from a JSON map
  factory IdCard.fromJson(Map<String, dynamic> json) {
    return IdCard(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      documentNumber: json['documentNumber'] as String,
      issuedBy: json['issuedBy'] as String,
      issuedDate: DateTime.parse(json['issuedDate'] as String),
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      status: TravelDocumentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => TravelDocumentStatus.active,
      ),
      notes: json['notes'] as String?,
      documentImageUrls: List<String>.from(json['documentImageUrls'] as List),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      fullName: json['fullName'] as String,
      dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
      gender: json['gender'] as String,
      address: json['address'] as String,
      nationality: json['nationality'] as String,
      idCardType: IdCardType.values.firstWhere(
        (e) => e.toString().split('.').last == json['idCardType'],
        orElse: () => IdCardType.nationalId,
      ),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString().split('.').last,
      'name': name,
      'documentNumber': documentNumber,
      'issuedBy': issuedBy,
      'issuedDate': issuedDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'status': status.toString().split('.').last,
      'notes': notes,
      'documentImageUrls': documentImageUrls,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'fullName': fullName,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'gender': gender,
      'address': address,
      'nationality': nationality,
      'idCardType': idCardType.toString().split('.').last,
    };
  }

  @override
  IdCard copyWith({
    String? id,
    String? userId,
    TravelDocumentType? type,
    String? name,
    String? documentNumber,
    String? issuedBy,
    DateTime? issuedDate,
    DateTime? expiryDate,
    TravelDocumentStatus? status,
    String? notes,
    List<String>? documentImageUrls,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? fullName,
    DateTime? dateOfBirth,
    String? gender,
    String? address,
    String? nationality,
    IdCardType? idCardType,
  }) {
    return IdCard(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      documentNumber: documentNumber ?? this.documentNumber,
      issuedBy: issuedBy ?? this.issuedBy,
      issuedDate: issuedDate ?? this.issuedDate,
      expiryDate: expiryDate ?? this.expiryDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      documentImageUrls: documentImageUrls ?? this.documentImageUrls,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      nationality: nationality ?? this.nationality,
      idCardType: idCardType ?? this.idCardType,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is IdCard &&
        other.id == id &&
        other.userId == userId &&
        other.name == name &&
        other.documentNumber == documentNumber &&
        other.issuedBy == issuedBy &&
        other.issuedDate == issuedDate &&
        other.expiryDate == expiryDate &&
        other.status == status &&
        other.notes == notes &&
        other.documentImageUrls == documentImageUrls &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.fullName == fullName &&
        other.dateOfBirth == dateOfBirth &&
        other.gender == gender &&
        other.address == address &&
        other.nationality == nationality &&
        other.idCardType == idCardType;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      userId,
      name,
      documentNumber,
      issuedBy,
      issuedDate,
      expiryDate,
      status,
      notes,
      documentImageUrls,
      createdAt,
      updatedAt,
      fullName,
      dateOfBirth,
      gender,
      address,
      nationality,
      idCardType,
    );
  }
}

/// Enum representing different types of ID cards
enum IdCardType {
  /// National ID card
  nationalId,

  /// Driver's license
  driversLicense,

  /// State ID card
  stateId,

  /// Resident card
  residentCard,

  /// Work permit
  workPermit,

  /// Student ID
  studentId,

  /// Other ID type
  other,
}

/// Extension for ID card types
extension IdCardTypeExtension on IdCardType {
  /// Get the display name for the ID card type
  String get displayName {
    switch (this) {
      case IdCardType.nationalId:
        return 'National ID';
      case IdCardType.driversLicense:
        return 'Driver\'s License';
      case IdCardType.stateId:
        return 'State ID';
      case IdCardType.residentCard:
        return 'Resident Card';
      case IdCardType.workPermit:
        return 'Work Permit';
      case IdCardType.studentId:
        return 'Student ID';
      case IdCardType.other:
        return 'Other';
    }
  }
}
